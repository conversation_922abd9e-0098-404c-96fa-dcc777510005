import { uploadFileServer } from '@/lib/server-file-upload';

export async function POST(request) {
  console.log('🚀 Pages upload endpoint called');

  try {
    const formData = await request.formData();
    console.log('📋 FormData received');

    const file = formData.get('file');

    if (!file) {
      console.log('❌ No file found');
      return Response.json({ success: false, error: 'No file provided' }, { status: 400 });
    }

    console.log('✅ File found:', file.name, file.size, file.type);

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      console.log('❌ Invalid file type:', file.type);
      return Response.json({
        success: false,
        error: `Invalid file type. Allowed: ${allowedTypes.join(', ')}`
      }, { status: 400 });
    }

    // Validate file size (15MB)
    const maxSize = 15 * 1024 * 1024;
    if (file.size > maxSize) {
      console.log('❌ File too large:', file.size);
      return Response.json({
        success: false,
        error: `File too large. Maximum size: ${maxSize / 1024 / 1024}MB`
      }, { status: 400 });
    }

    console.log('✅ File validation passed');

    // Upload file
    const result = await uploadFileServer(file, 'pages');

    if (result.success) {
      console.log('✅ Upload successful');
      return Response.json({
        success: true,
        url: result.url,
        path: result.path,
        filename: result.filename,
        size: result.size,
        type: result.type,
        storage: result.storage,
        message: 'File uploaded successfully'
      });
    } else {
      console.log('❌ Upload failed:', result.error);
      return Response.json({
        success: false,
        error: result.error || 'Upload failed'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ Upload error:', error);
    return Response.json({ success: false, error: error.message }, { status: 500 });
  }
}
