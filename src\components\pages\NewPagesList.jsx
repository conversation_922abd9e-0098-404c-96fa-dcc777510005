'use client';

import { useState } from 'react';
import { MdEdit, MdImage, MdLink, MdExpandMore, MdExpandLess } from 'react-icons/md';

const SECTIONS = [
  { 
    id: 'the island', 
    name: 'The Island', 
    hasAdditionalContent: true 
  },
  { 
    id: 'experiences', 
    name: 'Experiences', 
    hasAdditionalContent: true 
  },
  { 
    id: 'testimonials', 
    name: 'Testimonials', 
    hasAdditionalContent: false 
  },
  { 
    id: 'location & contacts', 
    name: 'Location & Contacts', 
    hasAdditionalContent: false 
  }
];

export default function NewPagesList({ 
  pages = {}, 
  onEdit, 
  isLoading = false 
}) {
  const [expandedSections, setExpandedSections] = useState({});

  const toggleSection = (sectionId) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse space-y-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-24 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!pages || Object.keys(pages).length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-8">
          <MdImage className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No pages found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first page content.
          </p>
          <div className="mt-6">
            <button
              onClick={onEdit}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <MdEdit className="-ml-1 mr-2 h-5 w-5" />
              Create Pages
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <div>
          <h2 className="text-xl font-bold text-gray-900">Pages Overview</h2>
          <p className="text-sm text-gray-600 mt-1">
            Manage content for all navbar sections
          </p>
        </div>
        <button
          onClick={onEdit}
          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <MdEdit className="-ml-1 mr-2 h-5 w-5" />
          Edit Pages
        </button>
      </div>

      {/* Sections List */}
      <div className="divide-y divide-gray-200">
        {SECTIONS.map((section) => {
          const sectionData = pages[section.id];
          const isExpanded = expandedSections[section.id];
          
          if (!sectionData) {
            return (
              <div key={section.id} className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">{section.name}</h3>
                    <p className="text-sm text-gray-500">No content available</p>
                  </div>
                  <div className="text-yellow-500">
                    <span className="text-sm">Not configured</span>
                  </div>
                </div>
              </div>
            );
          }

          return (
            <div key={section.id} className="p-6">
              {/* Section Header */}
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    {sectionData.image && (
                      <img
                        src={sectionData.image}
                        alt={section.name}
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                    )}
                    <div className="flex-1">
                      <h3 className="text-lg font-medium text-gray-900">{section.name}</h3>
                      {sectionData.title && (
                        <p className="text-sm font-medium text-gray-700 mt-1">{sectionData.title}</p>
                      )}
                      {sectionData.body && (
                        <p className="text-sm text-gray-600 mt-1 line-clamp-2">{sectionData.body}</p>
                      )}
                      
                      {/* URL for location & contacts */}
                      {section.id === 'location & contacts' && sectionData.url && (
                        <div className="flex items-center mt-2">
                          <MdLink className="w-4 h-4 text-gray-400 mr-1" />
                          <a
                            href={sectionData.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 hover:text-blue-800 truncate"
                          >
                            {sectionData.url}
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 ml-4">
                  {/* Additional Content Indicator */}
                  {section.hasAdditionalContent && sectionData.additionalContent?.length > 0 && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      {sectionData.additionalContent.length} additional
                    </span>
                  )}

                  {/* Expand/Collapse Button */}
                  {section.hasAdditionalContent && sectionData.additionalContent?.length > 0 && (
                    <button
                      onClick={() => toggleSection(section.id)}
                      className="p-1 text-gray-400 hover:text-gray-600"
                    >
                      {isExpanded ? (
                        <MdExpandLess className="w-5 h-5" />
                      ) : (
                        <MdExpandMore className="w-5 h-5" />
                      )}
                    </button>
                  )}

                  {/* Status Indicator */}
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    <span className="ml-2 text-sm text-gray-600">Active</span>
                  </div>
                </div>
              </div>

              {/* Additional Content (Expanded) */}
              {section.hasAdditionalContent && 
               sectionData.additionalContent?.length > 0 && 
               isExpanded && (
                <div className="mt-6 pl-4 border-l-2 border-gray-200">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Additional Content</h4>
                  <div className="space-y-3">
                    {sectionData.additionalContent.map((content, index) => (
                      <div key={index} className="bg-gray-50 p-3 rounded-lg">
                        <div className="flex items-start space-x-3">
                          {content.image && (
                            <img
                              src={content.image}
                              alt={content.title}
                              className="w-12 h-12 object-cover rounded"
                            />
                          )}
                          <div className="flex-1 min-w-0">
                            <h5 className="text-sm font-medium text-gray-900 truncate">
                              {content.title}
                            </h5>
                            <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                              {content.body}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Section Stats */}
              <div className="mt-4 flex items-center justify-between text-sm text-gray-500">
                <div className="flex items-center space-x-4">
                  <span>
                    Title: {sectionData.title ? '✓' : '✗'}
                  </span>
                  <span>
                    Body: {sectionData.body ? '✓' : '✗'}
                  </span>
                  <span>
                    Image: {sectionData.image ? '✓' : '✗'}
                  </span>
                  {section.id === 'location & contacts' && (
                    <span>
                      URL: {sectionData.url ? '✓' : '✗'}
                    </span>
                  )}
                  {section.hasAdditionalContent && (
                    <span>
                      Additional: {sectionData.additionalContent?.length || 0}
                    </span>
                  )}
                </div>
                <div>
                  Last updated: {pages.updatedAt ? new Date(pages.updatedAt).toLocaleDateString() : 'Never'}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Footer */}
      <div className="bg-gray-50 px-6 py-3 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>
            {SECTIONS.filter(section => pages[section.id]).length} of {SECTIONS.length} sections configured
          </span>
          <span>
            Total additional content: {
              SECTIONS.reduce((total, section) => {
                return total + (pages[section.id]?.additionalContent?.length || 0);
              }, 0)
            }
          </span>
        </div>
      </div>
    </div>
  );
}
