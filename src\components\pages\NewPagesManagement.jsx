'use client';

import { useState, useEffect, useCallback } from 'react';
import { MdRefresh, MdSettings } from 'react-icons/md';
import NewPagesForm from './NewPagesForm';
import NewPagesList from './NewPagesList';

export default function NewPagesManagement() {
  const [pages, setPages] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Fetch pages
  const fetchPages = useCallback(async () => {
    try {
      setIsLoading(true);
      setError('');
      
      const response = await fetch('/api/pages');
      const result = await response.json();

      console.log('NewPagesManagement: Fetched pages result:', result);

      if (result.success) {
        // Remove MongoDB metadata fields for clean data
        const cleanData = { ...result.data };
        delete cleanData._id;
        delete cleanData.__v;
        delete cleanData.createdAt;
        delete cleanData.updatedAt;
        
        console.log('NewPagesManagement: Setting clean pages data:', cleanData);
        setPages(cleanData);
      } else {
        setError(result.message || 'Failed to fetch pages');
        // Initialize with empty structure if no pages exist
        setPages({});
      }
    } catch (error) {
      console.error('Error fetching pages:', error);
      setError('Failed to fetch pages');
      setPages({});
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchPages();
  }, [fetchPages]);

  // Handle save
  const handleSave = useCallback(async (formData) => {
    try {
      setIsSaving(true);
      setError('');
      setSuccess('');

      console.log('NewPagesManagement: Saving form data:', formData);

      const response = await fetch('/api/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();
      console.log('NewPagesManagement: Save result:', result);

      if (result.success) {
        setSuccess('Pages saved successfully');
        setShowForm(false);
        await fetchPages(); // Refresh data
      } else {
        setError(result.message || 'Failed to save pages');
      }
    } catch (error) {
      console.error('Error saving pages:', error);
      setError('Failed to save pages');
    } finally {
      setIsSaving(false);
    }
  }, [fetchPages]);

  // Handle edit
  const handleEdit = useCallback(() => {
    setShowForm(true);
    setError('');
    setSuccess('');
  }, []);

  // Handle cancel
  const handleCancel = useCallback(() => {
    setShowForm(false);
    setError('');
    setSuccess('');
  }, []);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    await fetchPages();
    setSuccess('Pages refreshed successfully');
  }, [fetchPages]);

  // Handle reset to default structure
  const handleReset = useCallback(async () => {
    if (!confirm('Are you sure you want to reset all pages to default structure? This will remove all existing content.')) {
      return;
    }

    try {
      setIsLoading(true);
      setError('');
      
      const response = await fetch('/api/pages', {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('Pages reset to default structure successfully');
        await fetchPages();
      } else {
        setError(result.message || 'Failed to reset pages');
      }
    } catch (error) {
      console.error('Error resetting pages:', error);
      setError('Failed to reset pages');
    } finally {
      setIsLoading(false);
    }
  }, [fetchPages]);

  // Clear messages after timeout
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(''), 5000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(''), 8000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Pages Management</h1>
            <p className="text-gray-600 mt-1">
              Manage content for all navbar sections with enhanced structure
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <MdRefresh className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            <button
              onClick={handleReset}
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 border border-red-300 rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
            >
              <MdSettings className="w-4 h-4 mr-2" />
              Reset Structure
            </button>
          </div>
        </div>
      </div>

      {/* Success Message */}
      {success && (
        <div className="bg-green-50 border border-green-200 px-4 py-3 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 px-4 py-3 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
              <button
                onClick={fetchPages}
                className="mt-2 text-sm text-red-600 underline hover:no-underline"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      {showForm ? (
        <NewPagesForm
          pages={pages}
          onSave={handleSave}
          onCancel={handleCancel}
          isLoading={isSaving}
        />
      ) : (
        <NewPagesList
          pages={pages}
          onEdit={handleEdit}
          isLoading={isLoading}
        />
      )}

      {/* Debug Info (Development Only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-gray-100 rounded-lg p-4">
          <details>
            <summary className="cursor-pointer text-sm font-medium text-gray-700">
              Debug Information
            </summary>
            <div className="mt-2 text-xs text-gray-600">
              <div className="mb-2">
                <strong>Pages Data Structure:</strong>
                <pre className="mt-1 bg-white p-2 rounded border overflow-auto max-h-40">
                  {JSON.stringify(pages, null, 2)}
                </pre>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>State:</strong>
                  <ul className="mt-1 space-y-1">
                    <li>Loading: {isLoading ? 'Yes' : 'No'}</li>
                    <li>Saving: {isSaving ? 'Yes' : 'No'}</li>
                    <li>Show Form: {showForm ? 'Yes' : 'No'}</li>
                    <li>Has Error: {error ? 'Yes' : 'No'}</li>
                    <li>Has Success: {success ? 'Yes' : 'No'}</li>
                  </ul>
                </div>
                <div>
                  <strong>Pages Info:</strong>
                  <ul className="mt-1 space-y-1">
                    <li>Sections: {Object.keys(pages).filter(key => !['_id', '__v', 'createdAt', 'updatedAt'].includes(key)).length}</li>
                    <li>The Island: {pages['the island'] ? 'Configured' : 'Missing'}</li>
                    <li>Experiences: {pages['experiences'] ? 'Configured' : 'Missing'}</li>
                    <li>Testimonials: {pages['testimonials'] ? 'Configured' : 'Missing'}</li>
                    <li>Location & Contacts: {pages['location & contacts'] ? 'Configured' : 'Missing'}</li>
                  </ul>
                </div>
              </div>
            </div>
          </details>
        </div>
      )}
    </div>
  );
}
