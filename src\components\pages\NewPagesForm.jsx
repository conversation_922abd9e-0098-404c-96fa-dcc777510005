'use client';

import { useState, useEffect, useCallback } from 'react';
import { MdSave, MdCancel, MdCloudUpload, MdImage, MdDelete, MdAdd } from 'react-icons/md';

const SECTIONS = [
  { 
    id: 'the island', 
    name: 'The Island', 
    hasAdditionalContent: true,
    fields: ['image', 'title', 'body', 'additionalContent']
  },
  { 
    id: 'experiences', 
    name: 'Experiences', 
    hasAdditionalContent: true,
    fields: ['image', 'title', 'body', 'additionalContent']
  },
  { 
    id: 'testimonials', 
    name: 'Testimonials', 
    hasAdditionalContent: false,
    fields: ['image', 'title', 'body']
  },
  { 
    id: 'location & contacts', 
    name: 'Location & Contacts', 
    hasAdditionalContent: false,
    fields: ['image', 'title', 'body', 'url']
  }
];

export default function NewPagesForm({ 
  pages = {}, 
  onSave, 
  onCancel, 
  isLoading = false 
}) {
  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});
  const [uploading, setUploading] = useState(false);
  const [imagePreviews, setImagePreviews] = useState({});
  const [activeTab, setActiveTab] = useState('the island');
  const [showAdditionalContent, setShowAdditionalContent] = useState({});
  const [newAdditionalContent, setNewAdditionalContent] = useState({});

  // Initialize form data when pages prop changes
  useEffect(() => {
    console.log('NewPagesForm: Initializing with pages:', pages);

    const initialData = {};
    const initialPreviews = {};
    const initialShowAdditional = {};
    const initialNewContent = {};

    SECTIONS.forEach(section => {
      const existingSection = pages[section.id];

      if (section.hasAdditionalContent) {
        initialData[section.id] = {
          image: existingSection?.image || '',
          title: existingSection?.title || '',
          body: existingSection?.body || '',
          additionalContent: existingSection?.additionalContent || []
        };
        initialShowAdditional[section.id] = false;
        initialNewContent[section.id] = { image: '', title: '', body: '' };
      } else if (section.id === 'location & contacts') {
        initialData[section.id] = {
          image: existingSection?.image || '',
          title: existingSection?.title || '',
          body: existingSection?.body || '',
          url: existingSection?.url || ''
        };
      } else {
        initialData[section.id] = {
          image: existingSection?.image || '',
          title: existingSection?.title || '',
          body: existingSection?.body || ''
        };
      }

      if (existingSection?.image) {
        initialPreviews[section.id] = existingSection.image;
      }
    });

    setFormData(initialData);
    setImagePreviews(initialPreviews);
    setShowAdditionalContent(initialShowAdditional);
    setNewAdditionalContent(initialNewContent);
  }, [pages]);

  // Handle input changes
  const handleInputChange = useCallback((sectionId, field, value) => {
    setFormData(prev => ({
      ...prev,
      [sectionId]: {
        ...prev[sectionId],
        [field]: value
      }
    }));
    
    // Clear errors for this field
    if (errors[`${sectionId}.${field}`]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${sectionId}.${field}`];
        return newErrors;
      });
    }
  }, [errors]);

  // Handle image upload
  const handleImageUpload = useCallback(async (sectionId, file, isAdditional = false, additionalIndex = null) => {
    if (!file) return;

    try {
      setUploading(true);
      
      const formDataUpload = new FormData();
      formDataUpload.append('file', file);

      const response = await fetch('/api/upload/pages', {
        method: 'POST',
        body: formDataUpload,
      });

      const result = await response.json();

      if (result.success) {
        if (isAdditional && additionalIndex !== null) {
          // Handle additional content image
          setNewAdditionalContent(prev => ({
            ...prev,
            [sectionId]: {
              ...prev[sectionId],
              image: result.url
            }
          }));
        } else {
          // Handle main section image
          handleInputChange(sectionId, 'image', result.url);
          setImagePreviews(prev => ({
            ...prev,
            [sectionId]: result.url
          }));
        }
      } else {
        setErrors(prev => ({
          ...prev,
          [`${sectionId}.image`]: result.message || 'Upload failed'
        }));
      }
    } catch (error) {
      console.error('Upload error:', error);
      setErrors(prev => ({
        ...prev,
        [`${sectionId}.image`]: 'Upload failed'
      }));
    } finally {
      setUploading(false);
    }
  }, [handleInputChange]);

  // Handle additional content changes
  const handleAdditionalContentChange = useCallback((sectionId, field, value) => {
    setNewAdditionalContent(prev => ({
      ...prev,
      [sectionId]: {
        ...prev[sectionId],
        [field]: value
      }
    }));
  }, []);

  // Add additional content
  const addAdditionalContent = useCallback((sectionId) => {
    const newContent = newAdditionalContent[sectionId];
    
    if (!newContent.image || !newContent.title || !newContent.body) {
      setErrors(prev => ({
        ...prev,
        [`${sectionId}.additionalContent`]: 'All fields are required for additional content'
      }));
      return;
    }

    setFormData(prev => ({
      ...prev,
      [sectionId]: {
        ...prev[sectionId],
        additionalContent: [...(prev[sectionId].additionalContent || []), newContent]
      }
    }));

    // Reset new content form
    setNewAdditionalContent(prev => ({
      ...prev,
      [sectionId]: { image: '', title: '', body: '' }
    }));

    setShowAdditionalContent(prev => ({
      ...prev,
      [sectionId]: false
    }));

    // Clear errors
    if (errors[`${sectionId}.additionalContent`]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${sectionId}.additionalContent`];
        return newErrors;
      });
    }
  }, [newAdditionalContent, errors]);

  // Remove additional content
  const removeAdditionalContent = useCallback((sectionId, index) => {
    setFormData(prev => ({
      ...prev,
      [sectionId]: {
        ...prev[sectionId],
        additionalContent: prev[sectionId].additionalContent.filter((_, i) => i !== index)
      }
    }));
  }, []);

  // Validate form
  const validateForm = useCallback(() => {
    const newErrors = {};

    SECTIONS.forEach(section => {
      const sectionData = formData[section.id];
      
      if (!sectionData) {
        newErrors[`${section.id}.general`] = 'Section data is required';
        return;
      }

      // Validate required fields
      section.fields.forEach(field => {
        if (field === 'additionalContent') return; // Skip validation for additional content array
        
        if (field === 'url' && section.id !== 'location & contacts') return; // Skip URL for non-contact sections
        
        if (!sectionData[field] || sectionData[field].trim() === '') {
          newErrors[`${section.id}.${field}`] = `${field.charAt(0).toUpperCase() + field.slice(1)} is required`;
        }
      });
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle form submission
  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      await onSave(formData);
    } catch (error) {
      console.error('Form submission error:', error);
      setErrors({ submit: 'Failed to save pages. Please try again.' });
    }
  }, [formData, validateForm, onSave]);

  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 className="text-2xl font-bold text-gray-900">
          Pages Management
        </h2>
        <div className="flex space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <MdCancel className="w-4 h-4 mr-2 inline" />
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading || uploading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <MdSave className="w-4 h-4 mr-2 inline" />
            {isLoading || uploading ? 'Saving...' : 'Save All Pages'}
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 px-6">
          {SECTIONS.map((section) => (
            <button
              key={section.id}
              onClick={() => setActiveTab(section.id)}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === section.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {section.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {SECTIONS.map((section) => (
          activeTab === section.id && (
            <div key={section.id} className="space-y-6">
              {/* Main Section Content */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Image Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Section Image
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                    {imagePreviews[section.id] ? (
                      <div className="relative">
                        <img
                          src={imagePreviews[section.id]}
                          alt={`${section.name} preview`}
                          className="w-full h-48 object-cover rounded-lg"
                        />
                        <button
                          type="button"
                          onClick={() => {
                            setImagePreviews(prev => {
                              const newPreviews = { ...prev };
                              delete newPreviews[section.id];
                              return newPreviews;
                            });
                            handleInputChange(section.id, 'image', '');
                          }}
                          className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600"
                        >
                          <MdDelete className="w-4 h-4" />
                        </button>
                      </div>
                    ) : (
                      <div className="text-center">
                        <MdCloudUpload className="mx-auto h-12 w-12 text-gray-400" />
                        <div className="mt-2">
                          <label className="cursor-pointer">
                            <span className="mt-2 block text-sm font-medium text-gray-900">
                              Upload image
                            </span>
                            <input
                              type="file"
                              className="sr-only"
                              accept="image/*"
                              onChange={(e) => {
                                const file = e.target.files[0];
                                if (file) {
                                  handleImageUpload(section.id, file);
                                }
                              }}
                            />
                          </label>
                        </div>
                      </div>
                    )}
                  </div>
                  {errors[`${section.id}.image`] && (
                    <p className="mt-1 text-sm text-red-600">{errors[`${section.id}.image`]}</p>
                  )}
                </div>

                {/* Title and Body */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Title
                    </label>
                    <input
                      type="text"
                      value={formData[section.id]?.title || ''}
                      onChange={(e) => handleInputChange(section.id, 'title', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder={`Enter ${section.name} title`}
                    />
                    {errors[`${section.id}.title`] && (
                      <p className="mt-1 text-sm text-red-600">{errors[`${section.id}.title`]}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Body Content
                    </label>
                    <textarea
                      value={formData[section.id]?.body || ''}
                      onChange={(e) => handleInputChange(section.id, 'body', e.target.value)}
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder={`Enter ${section.name} content`}
                    />
                    {errors[`${section.id}.body`] && (
                      <p className="mt-1 text-sm text-red-600">{errors[`${section.id}.body`]}</p>
                    )}
                  </div>

                  {/* URL field for location & contacts */}
                  {section.id === 'location & contacts' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Contact URL
                      </label>
                      <input
                        type="url"
                        value={formData[section.id]?.url || ''}
                        onChange={(e) => handleInputChange(section.id, 'url', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="https://example.com"
                      />
                      {errors[`${section.id}.url`] && (
                        <p className="mt-1 text-sm text-red-600">{errors[`${section.id}.url`]}</p>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Additional Content Section */}
              {section.hasAdditionalContent && (
                <div className="border-t pt-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">Additional Content</h3>
                    <button
                      type="button"
                      onClick={() => setShowAdditionalContent(prev => ({
                        ...prev,
                        [section.id]: !prev[section.id]
                      }))}
                      className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      <MdAdd className="w-4 h-4 mr-2 inline" />
                      More Content
                    </button>
                  </div>

                  {/* Existing Additional Content */}
                  {formData[section.id]?.additionalContent?.length > 0 && (
                    <div className="space-y-4 mb-6">
                      {formData[section.id].additionalContent.map((content, index) => (
                        <div key={index} className="bg-gray-50 p-4 rounded-lg">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-4 mb-2">
                                {content.image && (
                                  <img
                                    src={content.image}
                                    alt={content.title}
                                    className="w-16 h-16 object-cover rounded"
                                  />
                                )}
                                <div>
                                  <h4 className="font-medium text-gray-900">{content.title}</h4>
                                  <p className="text-sm text-gray-600 line-clamp-2">{content.body}</p>
                                </div>
                              </div>
                            </div>
                            <button
                              type="button"
                              onClick={() => removeAdditionalContent(section.id, index)}
                              className="ml-4 text-red-600 hover:text-red-800"
                            >
                              <MdDelete className="w-5 h-5" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Add New Additional Content Form */}
                  {showAdditionalContent[section.id] && (
                    <div className="bg-blue-50 p-4 rounded-lg space-y-4">
                      <h4 className="font-medium text-gray-900">Add New Content</h4>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* Additional Content Image */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Image
                          </label>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-2">
                            {newAdditionalContent[section.id]?.image ? (
                              <div className="relative">
                                <img
                                  src={newAdditionalContent[section.id].image}
                                  alt="Additional content preview"
                                  className="w-full h-24 object-cover rounded"
                                />
                                <button
                                  type="button"
                                  onClick={() => handleAdditionalContentChange(section.id, 'image', '')}
                                  className="absolute top-1 right-1 bg-red-500 text-white p-1 rounded-full hover:bg-red-600"
                                >
                                  <MdDelete className="w-3 h-3" />
                                </button>
                              </div>
                            ) : (
                              <div className="text-center">
                                <label className="cursor-pointer">
                                  <MdCloudUpload className="mx-auto h-8 w-8 text-gray-400" />
                                  <span className="block text-xs text-gray-600 mt-1">Upload</span>
                                  <input
                                    type="file"
                                    className="sr-only"
                                    accept="image/*"
                                    onChange={async (e) => {
                                      const file = e.target.files[0];
                                      if (file) {
                                        try {
                                          setUploading(true);
                                          const formDataUpload = new FormData();
                                          formDataUpload.append('file', file);

                                          const response = await fetch('/api/upload/pages', {
                                            method: 'POST',
                                            body: formDataUpload,
                                          });

                                          const result = await response.json();

                                          if (result.success) {
                                            handleAdditionalContentChange(section.id, 'image', result.url);
                                          }
                                        } catch (error) {
                                          console.error('Upload error:', error);
                                        } finally {
                                          setUploading(false);
                                        }
                                      }
                                    }}
                                  />
                                </label>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Additional Content Title */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Title
                          </label>
                          <input
                            type="text"
                            value={newAdditionalContent[section.id]?.title || ''}
                            onChange={(e) => handleAdditionalContentChange(section.id, 'title', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Content title"
                          />
                        </div>

                        {/* Additional Content Body */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Body
                          </label>
                          <textarea
                            value={newAdditionalContent[section.id]?.body || ''}
                            onChange={(e) => handleAdditionalContentChange(section.id, 'body', e.target.value)}
                            rows={3}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Content body"
                          />
                        </div>
                      </div>

                      {/* Add Content Button */}
                      <div className="flex justify-end space-x-2">
                        <button
                          type="button"
                          onClick={() => setShowAdditionalContent(prev => ({
                            ...prev,
                            [section.id]: false
                          }))}
                          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                        >
                          Cancel
                        </button>
                        <button
                          type="button"
                          onClick={() => addAdditionalContent(section.id)}
                          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                        >
                          Add This Content
                        </button>
                      </div>

                      {errors[`${section.id}.additionalContent`] && (
                        <p className="text-sm text-red-600">{errors[`${section.id}.additionalContent`]}</p>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          )
        ))}
      </div>

      {/* Submit Error */}
      {errors.submit && (
        <div className="px-6 pb-6">
          <div className="bg-red-50 border border-red-200 px-4 py-3 rounded-md">
            <p className="text-red-600">{errors.submit}</p>
          </div>
        </div>
      )}
    </div>
  );
}
