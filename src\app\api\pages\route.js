import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Page } from '@/models/Page';

// GET /api/pages - Get all pages with search and filtering (no authentication required)
export async function GET(request) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit')) || 50;
    const page = parseInt(searchParams.get('page')) || 1;
    const sort = searchParams.get('sort') || 'section';
    const section = searchParams.get('section');

    // Build query
    const query = {};

    // Search by section or text content
    if (search) {
      query.$or = [
        { section: { $regex: search, $options: 'i' } },
        { text: { $regex: search, $options: 'i' } }
      ];
    }

    // Filter by specific section
    if (section) {
      query.section = section;
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const items = await Page.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await Page.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: items,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      message: `Found ${items.length} page(s)`,
    });
  } catch (error) {
    console.error('Error fetching pages:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch pages',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// POST /api/pages - Create new page (no authentication required)
export async function POST(request) {
  try {
    await connectDB();

    const body = await request.json();

    // Handle both single page and bulk creation
    if (Array.isArray(body)) {
      // Bulk creation
      const createdPages = [];
      
      for (const pageData of body) {
        // Debug logging for Location & Contacts section
        if (pageData.section === 'location & contacts') {
          console.log('API: Processing Location & Contacts data:', pageData);
        }

        // Check if page with this section already exists
        const existingPage = await Page.findOne({ section: pageData.section });

        if (existingPage) {
          // Update existing page instead of creating duplicate
          const updatedPage = await Page.findByIdAndUpdate(
            existingPage._id,
            pageData,
            { new: true, runValidators: true }
          );

          // Debug logging for Location & Contacts section
          if (pageData.section === 'location & contacts') {
            console.log('API: Updated Location & Contacts page:', updatedPage);
          }

          createdPages.push(updatedPage);
        } else {
          // Create new page
          const newPage = new Page(pageData);
          const savedPage = await newPage.save();

          // Debug logging for Location & Contacts section
          if (pageData.section === 'location & contacts') {
            console.log('API: Created new Location & Contacts page:', savedPage);
          }

          createdPages.push(savedPage);
        }
      }

      return NextResponse.json({
        success: true,
        data: createdPages,
        message: `${createdPages.length} page(s) processed successfully`,
      });
    } else {
      // Single page creation
      // Check if page with this section already exists
      const existingPage = await Page.findOne({ section: body.section });
      
      if (existingPage) {
        return NextResponse.json(
          {
            success: false,
            error: 'Validation Error',
            message: `Page for section '${body.section}' already exists`,
          },
          { status: 400 }
        );
      }

      const newPage = new Page(body);
      const savedPage = await newPage.save();

      return NextResponse.json({
        success: true,
        data: savedPage,
        message: 'Page created successfully',
      });
    }
  } catch (error) {
    console.error('Error creating page:', error);

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }

    if (error.code === 11000) {
      return NextResponse.json(
        {
          success: false,
          error: 'Duplicate Error',
          message: 'A page with this section already exists',
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create page',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PATCH /api/pages - Bulk update pages (no authentication required)
export async function PATCH(request) {
  try {
    await connectDB();

    const body = await request.json();
    const { items, action } = body;

    if (!Array.isArray(items)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'items must be an array',
        },
        { status: 400 }
      );
    }

    let result;

    switch (action) {
      case 'delete':
        result = await Page.deleteMany({
          _id: { $in: items }
        });
        break;

      case 'update':
        // Bulk update - items should contain objects with _id and update data
        const updatePromises = items.map(item => 
          Page.findByIdAndUpdate(
            item._id,
            { $set: item.data },
            { new: true, runValidators: true }
          )
        );
        result = await Promise.all(updatePromises);
        break;

      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid Action',
            message: 'Action must be "delete" or "update"',
          },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      data: result,
      message: `Bulk ${action} completed successfully`,
    });
  } catch (error) {
    console.error('Error in bulk operation:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Bulk operation failed',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// DELETE /api/pages - Bulk delete pages (no authentication required)
export async function DELETE(request) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const ids = searchParams.get('ids');

    if (!ids) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'No page IDs provided',
        },
        { status: 400 }
      );
    }

    const idArray = ids.split(',');
    const result = await Page.deleteMany({
      _id: { $in: idArray }
    });

    return NextResponse.json({
      success: true,
      data: { deletedCount: result.deletedCount },
      message: `${result.deletedCount} page(s) deleted successfully`,
    });
  } catch (error) {
    console.error('Error deleting pages:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete pages',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
