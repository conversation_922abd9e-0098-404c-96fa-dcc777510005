import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Page } from '@/models/Page';

// GET /api/pages - Get pages document (no authentication required)
export async function GET(request) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const section = searchParams.get('section');

    // Get the single pages document
    let pages = await Page.findOne().lean();

    // Initialize if doesn't exist
    if (!pages) {
      await Page.initializeDefaultPages();
      pages = await Page.findOne().lean();
    }

    // If specific section requested, return just that section
    if (section && pages[section]) {
      return NextResponse.json({
        success: true,
        data: { [section]: pages[section] },
        message: `${section} section retrieved successfully`,
      });
    }

    return NextResponse.json({
      success: true,
      data: pages,
      message: 'Pages retrieved successfully',
    });
  } catch (error) {
    console.error('Error fetching pages:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to fetch pages',
      },
      { status: 500 }
    );
  }
}

// POST /api/pages - Update pages document (no authentication required)
export async function POST(request) {
  try {
    await connectDB();

    const body = await request.json();

    // Get existing pages document or create new one
    let pages = await Page.findOne();

    if (!pages) {
      // Create new pages document with provided data
      pages = new Page(body);
      const savedPages = await pages.save();

      return NextResponse.json({
        success: true,
        data: savedPages,
        message: 'Pages created successfully',
      });
    } else {
      // Update existing pages document
      const updatedPages = await Page.findByIdAndUpdate(
        pages._id,
        body,
        { new: true, runValidators: true }
      );

      return NextResponse.json({
        success: true,
        data: updatedPages,
        message: 'Pages updated successfully',
      });
    }
  } catch (error) {
    console.error('Error saving pages:', error);

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to save pages',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PATCH /api/pages - Partial update pages document (no authentication required)
export async function PATCH(request) {
  try {
    await connectDB();

    const body = await request.json();

    // Get existing pages document
    let pages = await Page.findOne();

    if (!pages) {
      // Initialize if doesn't exist
      await Page.initializeDefaultPages();
      pages = await Page.findOne();
    }

    // Update specific sections
    const updatedPages = await Page.findByIdAndUpdate(
      pages._id,
      { $set: body },
      { new: true, runValidators: true }
    );

    return NextResponse.json({
      success: true,
      data: updatedPages,
      message: 'Pages updated successfully',
    });
  } catch (error) {
    console.error('Error updating pages:', error);

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update pages',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// DELETE /api/pages - Reset pages to default (no authentication required)
export async function DELETE(request) {
  try {
    await connectDB();

    // Drop and recreate with new structure
    const result = await Page.migrateToNewStructure();

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Pages reset to default structure successfully',
    });
  } catch (error) {
    console.error('Error resetting pages:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to reset pages',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
