'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { MdCloudUpload, MdDelete, MdImage, MdLink } from 'react-icons/md';

const SECTIONS = [
  { id: 'the island', title: 'The Island', hasUrl: false },
  { id: 'experiences', title: 'Experiences', hasUrl: false },
  { id: 'testimonials', title: 'Testimonials', hasUrl: false },
  { id: 'location & contacts', title: 'Location & Contacts', hasUrl: true }
];

export default function PagesForm({
  pages = [],
  onSave,
  onCancel,
  isLoading = false
}) {
  const [formData, setFormData] = useState({});
  const [imageFiles, setImageFiles] = useState({});
  const [imagePreviews, setImagePreviews] = useState({});
  const [uploading, setUploading] = useState(false);
  const [errors, setErrors] = useState({});

  // Initialize form data when pages prop changes
  useEffect(() => {
    console.log('PagesForm: Initializing with pages:', pages);

    const initialData = {};
    const initialPreviews = {};

    SECTIONS.forEach(section => {
      const existingPage = pages.find(p => p.section === section.id);

      initialData[section.id] = {
        section: section.id,
        text: existingPage?.text || '',
        image: existingPage?.image || '',
        url: existingPage?.url || ''
      };

      // Debug logging for Location & Contacts section
      if (section.id === 'location & contacts') {
        console.log('Location & Contacts initialization:', {
          existingPage,
          existingUrl: existingPage?.url,
          initialDataUrl: initialData[section.id].url
        });
      }

      if (existingPage?.image) {
        initialPreviews[section.id] = existingPage.image;
      }
    });

    console.log('PagesForm: Setting initial form data:', initialData);
    setFormData(initialData);
    setImagePreviews(initialPreviews);
    setImageFiles({});
  }, [pages]);

  const handleTextChange = useCallback((section, value) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        text: value
      }
    }));

    // Clear text error if it exists
    if (errors[`${section}_text`]) {
      setErrors(prev => ({
        ...prev,
        [`${section}_text`]: ''
      }));
    }
  }, [errors]);

  const handleUrlChange = useCallback((section, value) => {
    console.log('URL change handler called:', { section, value });

    setFormData(prev => {
      const newData = {
        ...prev,
        [section]: {
          ...prev[section],
          url: value
        }
      };
      console.log('Updated formData after URL change:', newData);
      return newData;
    });

    // Clear URL error if it exists
    if (errors[`${section}_url`]) {
      setErrors(prev => ({
        ...prev,
        [`${section}_url`]: ''
      }));
    }
  }, [errors]);

  const handleImageChange = useCallback((section, e) => {
    const file = e.target.files[0];
    if (file) {
      setImageFiles(prev => ({
        ...prev,
        [section]: file
      }));

      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      setImagePreviews(prev => ({
        ...prev,
        [section]: previewUrl
      }));

      // Clear image error if it exists
      if (errors[`${section}_image`]) {
        setErrors(prev => ({
          ...prev,
          [`${section}_image`]: ''
        }));
      }
    }
  }, [errors]);

  const removePreview = useCallback((section) => {
    setImagePreviews(prev => {
      const newPreviews = { ...prev };
      delete newPreviews[section];
      return newPreviews;
    });
    
    setImageFiles(prev => {
      const newFiles = { ...prev };
      delete newFiles[section];
      return newFiles;
    });

    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        image: ''
      }
    }));
  }, []);

  const uploadImages = async () => {
    const uploadResults = {};
    const sectionsWithFiles = Object.keys(imageFiles);

    if (sectionsWithFiles.length === 0) return uploadResults;

    setUploading(true);
    try {
      for (const section of sectionsWithFiles) {
        const file = imageFiles[section];
        const uploadFormData = new FormData();
        uploadFormData.append('files', file);

        const response = await fetch('/api/upload/pages', {
          method: 'POST',
          body: uploadFormData,
        });

        const result = await response.json();

        if (result.success && result.data.length > 0) {
          uploadResults[section] = result.data[0];
        } else {
          throw new Error(`Upload failed for ${section}`);
        }
      }

      return uploadResults;
    } catch (error) {
      console.error('Images upload error:', error);
      throw error;
    } finally {
      setUploading(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    SECTIONS.forEach(section => {
      const data = formData[section.id];
      
      // Validate text content
      if (!data?.text?.trim()) {
        newErrors[`${section.id}_text`] = 'Text content is required';
      }

      // Validate image
      if (!data?.image && !imageFiles[section.id]) {
        newErrors[`${section.id}_image`] = 'Image is required';
      }

      // Validate URL for location & contacts section
      if (section.hasUrl && data?.url && !/^https?:\/\/.+/.test(data.url)) {
        newErrors[`${section.id}_url`] = 'Please enter a valid URL';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      // Upload new images
      const uploadResults = await uploadImages();

      // Prepare page data for submission
      const pageData = SECTIONS.map(section => {
        const data = formData[section.id];
        const uploadResult = uploadResults[section.id];

        const pageItem = {
          section: section.id,
          text: data.text,
          image: uploadResult ? uploadResult.url : data.image,
          url: section.hasUrl ? data.url : '',
          ...(uploadResult && {
            fullPath: uploadResult.path || `pages/${uploadResult.filename}`,
            contentType: uploadResult.type || imageFiles[section.id]?.type,
            size: uploadResult.size || imageFiles[section.id]?.size,
            uploadedAt: new Date().toISOString()
          })
        };

        // Debug logging for Location & Contacts section
        if (section.id === 'location & contacts') {
          console.log('Location & Contacts data preparation:', {
            sectionId: section.id,
            hasUrl: section.hasUrl,
            formDataUrl: data.url,
            finalUrl: pageItem.url,
            fullData: data
          });
        }

        return pageItem;
      });

      console.log('Final pageData being submitted:', pageData);
      await onSave(pageData);
    } catch (error) {
      console.error('Form submission error:', error);
      setErrors({ submit: 'Failed to save pages. Please try again.' });
    }
  };

  const memoizedSections = useMemo(() => SECTIONS, []);

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">
          Pages Management
        </h2>
        <div className="flex space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading || uploading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isLoading || uploading ? 'Saving...' : 'Save All Pages'}
          </button>
        </div>
      </div>

      {errors.submit && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {errors.submit}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-8">
        {memoizedSections.map((section) => (
          <div key={section.id} className="border border-gray-200 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              {section.title}
            </h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Text Content */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Content Text *
                </label>
                <textarea
                  value={formData[section.id]?.text || ''}
                  onChange={(e) => handleTextChange(section.id, e.target.value)}
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={`Enter content for ${section.title}...`}
                />
                {errors[`${section.id}_text`] && (
                  <p className="mt-1 text-sm text-red-600">{errors[`${section.id}_text`]}</p>
                )}

                {/* URL field for location & contacts */}
                {section.hasUrl && (
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <MdLink className="inline mr-1" />
                      External Link URL
                    </label>
                    <input
                      type="url"
                      value={formData[section.id]?.url || ''}
                      onChange={(e) => handleUrlChange(section.id, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="https://example.com"
                    />
                    {errors[`${section.id}_url`] && (
                      <p className="mt-1 text-sm text-red-600">{errors[`${section.id}_url`]}</p>
                    )}
                  </div>
                )}
              </div>

              {/* Image Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Section Image *
                </label>
                
                {/* Image Preview */}
                {imagePreviews[section.id] && (
                  <div className="mb-4 relative">
                    <img
                      src={imagePreviews[section.id]}
                      alt={`${section.title} preview`}
                      className="w-full h-48 object-cover rounded-lg border border-gray-300"
                    />
                    <button
                      type="button"
                      onClick={() => removePreview(section.id)}
                      className="absolute top-2 right-2 p-1 bg-red-600 text-white rounded-full hover:bg-red-700"
                    >
                      <MdDelete size={16} />
                    </button>
                  </div>
                )}

                {/* File Input */}
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleImageChange(section.id, e)}
                    className="hidden"
                    id={`image-${section.id}`}
                  />
                  <label
                    htmlFor={`image-${section.id}`}
                    className="cursor-pointer flex flex-col items-center"
                  >
                    <MdCloudUpload className="text-4xl text-gray-400 mb-2" />
                    <span className="text-sm text-gray-600">
                      Click to upload image for {section.title}
                    </span>
                    <span className="text-xs text-gray-500 mt-1">
                      PNG, JPG, GIF up to 15MB
                    </span>
                  </label>
                </div>

                {errors[`${section.id}_image`] && (
                  <p className="mt-1 text-sm text-red-600">{errors[`${section.id}_image`]}</p>
                )}
              </div>
            </div>
          </div>
        ))}
      </form>
    </div>
  );
}
