import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Page } from '@/models/Page';

// GET /api/pages/[id] - Get specific section (no authentication required)
export async function GET(request, { params }) {
  try {
    await connectDB();
    const { id } = await params;

    // Get the pages document
    let pages = await Page.findOne().lean();

    if (!pages) {
      // Initialize if doesn't exist
      await Page.initializeDefaultPages();
      pages = await Page.findOne().lean();
    }

    // Check if the section exists
    if (!pages[id]) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: `Section '${id}' not found`,
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { [id]: pages[id] },
      message: `${id} section retrieved successfully`,
    });
  } catch (error) {
    console.error('Error fetching section:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to fetch section',
      },
      { status: 500 }
    );
  }
}

// PUT /api/pages/[id] - Update specific section (no authentication required)
export async function PUT(request, { params }) {
  try {
    await connectDB();
    const { id } = await params;
    const body = await request.json();

    // Get existing pages document
    let pages = await Page.findOne();

    if (!pages) {
      // Initialize if doesn't exist
      await Page.initializeDefaultPages();
      pages = await Page.findOne();
    }

    // Check if the section exists
    if (!pages[id]) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: `Section '${id}' not found`,
        },
        { status: 404 }
      );
    }

    // Update the specific section
    const updateData = { [id]: body };
    const updatedPages = await Page.findByIdAndUpdate(
      pages._id,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    return NextResponse.json({
      success: true,
      data: { [id]: updatedPages[id] },
      message: `${id} section updated successfully`,
    });
  } catch (error) {
    console.error('Error updating section:', error);

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update section',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PATCH /api/pages/[id] - Partial update specific section (no authentication required)
export async function PATCH(request, { params }) {
  try {
    await connectDB();
    const { id } = await params;
    const body = await request.json();

    // Get existing pages document
    let pages = await Page.findOne();

    if (!pages) {
      // Initialize if doesn't exist
      await Page.initializeDefaultPages();
      pages = await Page.findOne();
    }

    // Check if the section exists
    if (!pages[id]) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: `Section '${id}' not found`,
        },
        { status: 404 }
      );
    }

    // Merge the update with existing section data
    const currentSection = pages[id];
    const updatedSection = { ...currentSection, ...body };

    // Update the specific section
    const updateData = { [id]: updatedSection };
    const updatedPages = await Page.findByIdAndUpdate(
      pages._id,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    return NextResponse.json({
      success: true,
      data: { [id]: updatedPages[id] },
      message: `${id} section updated successfully`,
    });
  } catch (error) {
    console.error('Error updating section:', error);

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update section',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// DELETE /api/pages/[id] - Reset specific section to default (no authentication required)
export async function DELETE(request, { params }) {
  try {
    await connectDB();
    const { id } = await params;

    // Get existing pages document
    let pages = await Page.findOne();

    if (!pages) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Pages document not found',
        },
        { status: 404 }
      );
    }

    // Check if the section exists
    if (!pages[id]) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: `Section '${id}' not found`,
        },
        { status: 404 }
      );
    }

    // Reset section to default based on section type
    let defaultSection;
    if (id === 'the island' || id === 'experiences') {
      defaultSection = {
        image: 'https://firebasestorage.googleapis.com/placeholder.jpg',
        title: `Welcome to ${id === 'the island' ? 'The Island' : 'Unique Experiences'}`,
        body: `This is the default content for ${id}. Update through the admin panel.`,
        additionalContent: []
      };
    } else if (id === 'testimonials') {
      defaultSection = {
        image: 'https://firebasestorage.googleapis.com/placeholder.jpg',
        title: 'Guest Testimonials',
        body: 'Read what our guests have to say about their experiences.'
      };
    } else if (id === 'location & contacts') {
      defaultSection = {
        image: 'https://firebasestorage.googleapis.com/placeholder.jpg',
        title: 'Location & Contact Information',
        body: 'Find us and get in touch for your next adventure.',
        url: 'https://example.com'
      };
    }

    // Update the specific section
    const updateData = { [id]: defaultSection };
    const updatedPages = await Page.findByIdAndUpdate(
      pages._id,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    return NextResponse.json({
      success: true,
      data: { [id]: updatedPages[id] },
      message: `${id} section reset to default successfully`,
    });
  } catch (error) {
    console.error('Error resetting section:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to reset section',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
