import mongoose from 'mongoose';
const { Schema } = mongoose;

// Schema for additional content entries
const AdditionalContentSchema = new Schema({
  image: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        return /^https:\/\/firebasestorage\.googleapis\.com/.test(v);
      },
      message: 'Image must be a valid Firebase Storage URL'
    }
  },
  title: {
    type: String,
    required: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  body: {
    type: String,
    required: true,
    maxlength: [2000, 'Body content cannot exceed 2000 characters']
  }
}, { _id: true });

// Schema for main content sections (the island, experiences)
const MainContentSchema = new Schema({
  image: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        return /^https:\/\/firebasestorage\.googleapis\.com/.test(v);
      },
      message: 'Image must be a valid Firebase Storage URL'
    }
  },
  title: {
    type: String,
    required: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  body: {
    type: String,
    required: true,
    maxlength: [5000, 'Body content cannot exceed 5000 characters']
  },
  additionalContent: {
    type: [AdditionalContentSchema],
    default: []
  }
}, { _id: false });

// Schema for testimonials section
const TestimonialsSchema = new Schema({
  image: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        return /^https:\/\/firebasestorage\.googleapis\.com/.test(v);
      },
      message: 'Image must be a valid Firebase Storage URL'
    }
  },
  title: {
    type: String,
    required: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  body: {
    type: String,
    required: true,
    maxlength: [5000, 'Body content cannot exceed 5000 characters']
  }
}, { _id: false });

// Schema for location & contacts section
const LocationContactsSchema = new Schema({
  image: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        return /^https:\/\/firebasestorage\.googleapis\.com/.test(v);
      },
      message: 'Image must be a valid Firebase Storage URL'
    }
  },
  title: {
    type: String,
    required: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  body: {
    type: String,
    required: true,
    maxlength: [5000, 'Body content cannot exceed 5000 characters']
  },
  url: {
    type: String,
    default: '',
    validate: {
      validator: function(v) {
        if (!v) return true; // Allow empty string
        return /^https?:\/\/.+/.test(v);
      },
      message: 'URL must be a valid HTTP/HTTPS URL'
    }
  }
}, { _id: false });

const PageSchema = new Schema({
  // Single document containing all four navbar sections
  'the island': {
    type: MainContentSchema,
    required: true
  },

  experiences: {
    type: MainContentSchema,
    required: true
  },

  testimonials: {
    type: TestimonialsSchema,
    required: true
  },

  'location & contacts': {
    type: LocationContactsSchema,
    required: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
PageSchema.index({ createdAt: -1 });

// Static method to initialize default pages structure
PageSchema.statics.initializeDefaultPages = async function() {
  // Check if pages document already exists
  const existingPages = await this.findOne();

  if (!existingPages) {
    const defaultPages = {
      'the island': {
        image: 'https://firebasestorage.googleapis.com/placeholder.jpg',
        title: 'Welcome to The Island',
        body: 'Discover the beauty and tranquility of Elephant Island Lodge. This content can be updated through the admin panel.',
        additionalContent: []
      },
      experiences: {
        image: 'https://firebasestorage.googleapis.com/placeholder.jpg',
        title: 'Unique Experiences',
        body: 'Explore our range of unique experiences designed to create lasting memories. This content can be updated through the admin panel.',
        additionalContent: []
      },
      testimonials: {
        image: 'https://firebasestorage.googleapis.com/placeholder.jpg',
        title: 'Guest Testimonials',
        body: 'Read what our guests have to say about their unforgettable experiences at Elephant Island Lodge.'
      },
      'location & contacts': {
        image: 'https://firebasestorage.googleapis.com/placeholder.jpg',
        title: 'Location & Contact Information',
        body: 'Find us and get in touch for your next adventure at Elephant Island Lodge.',
        url: 'https://example.com'
      }
    };

    await this.create(defaultPages);
  }
};

// Static method to drop and recreate collection with new structure
PageSchema.statics.migrateToNewStructure = async function() {
  try {
    // Drop the existing collection
    await this.collection.drop();
    console.log('✅ Dropped existing Pages collection');

    // Initialize with new structure
    await this.initializeDefaultPages();
    console.log('✅ Initialized Pages collection with new structure');

    return { success: true, message: 'Migration completed successfully' };
  } catch (error) {
    if (error.code === 26) {
      // Collection doesn't exist, just initialize
      await this.initializeDefaultPages();
      console.log('✅ Initialized new Pages collection');
      return { success: true, message: 'New collection initialized' };
    }
    throw error;
  }
};

export const Page = mongoose.models.Page || mongoose.model('Page', PageSchema);
